"""
Translation module for the novel translation tool.

This module provides the Translator class for handling text translation
using the Gemini API with support for glossaries and image tag preservation.
"""

import json
import time
import os
import re
from vertexai.generative_models import GenerativeModel, GenerationConfig
from config.config import SAFETY_SETTING
from glossary.glossary import Glossary
import concurrent.futures
from translation.prompt_templates import get_translation_prompt
from translation.fallback_templates import get_fallback_prompt


class Translator:
    """
    Handles text translation using the Gemini API.
    
    This class provides functionality for:
    - Loading and using glossaries
    - Preserving image tags during translation
    - Handling prohibited content
    - Retrying failed translations
    """
    
    
    def build_instructions(self, base, glossary_text=None):
        lang_hint = f"Translate the following {self.source_lang} text into fluent English."
        instructions = [lang_hint] + base
        if glossary_text:
            instructions.append(glossary_text)
        return instructions


    def __init__(self, glossary_file=None, source_lang='Japanese'):
        """
        Initialize the translator.
        
        Args:
            glossary_file: Optional path to the glossary file to use for translation
        """
        self.source_lang = source_lang
        self.model_name = "gemini-2.0-flash-exp"  # Track what model is active
        self.initialize_model(self.model_name)
        self.glossary = Glossary(glossary_file)
        self.model = GenerativeModel(
            model_name="gemini-2.0-flash-exp",
            safety_settings=SAFETY_SETTING,
            generation_config=GenerationConfig(
                temperature=0.4,
                top_p=0.95,
                top_k=40
            )
        )

    def initialize_model(self, model_name):
        self.model_name = model_name
        self.model = GenerativeModel(
            model_name=self.model_name,
            safety_settings=SAFETY_SETTING,
            generation_config=GenerationConfig(
                temperature=0.4,
                top_p=0.95,
                top_k=40
            )
        )

    def get_name_glossary(self):
        try:
            current_glossary = self.glossary.get_current_glossary_file()
            if not current_glossary:
                return ""
                
            glossary_name = os.path.splitext(os.path.basename(current_glossary))[0]
            glossary_dir = os.path.dirname(current_glossary)
            name_glossary_path = os.path.join(glossary_dir, glossary_name, "name_glossary.txt")
            
            if not os.path.exists(name_glossary_path):
                print(f"[WARNING] Name glossary not found at: {name_glossary_path}")
                print(f"[INFO] Expected subfolder name: {glossary_name}")
                return ""
            
            with open(name_glossary_path, "r", encoding="utf-8") as f:
                content = f.read()
                # Extract content between markers
                parts = content.split("==================================== GLOSSARY START ===============================")
                if len(parts) > 1:
                    glossary_text = parts[1].split("==================================== GLOSSARY END ================================")[0].strip()
                    return glossary_text if glossary_text else ""
                return ""
        except Exception as e:
            print(f"[ERROR] Failed to read name glossary from {name_glossary_path}: {e}")
            return ""
        
    def generate_with_instructions(self, prompt, instructions, instructions_label, log_message,
                                    max_retries=2, retry_delay=90, cancel_flag=None):
        """
        Attempts translation using the provided instructions (primary or secondary).
        Retries on certain errors. If blocked for prohibited content, raises RuntimeError.
        Supports cancellation via cancel_flag.
        """
        log_message(f"[{instructions_label}] Attempting generation...")

        # Prepend instructions to prompt
        full_prompt = "\n".join(self.build_instructions(instructions, glossary_text=None)) + "\n\n" + prompt
        tl_model = self.model

        attempt = 0
        while attempt < max_retries:
            # Check for cancellation before each attempt
            if cancel_flag and cancel_flag():
                log_message(f"[{instructions_label}] Generation cancelled by user.")
                raise RuntimeError("GENERATION_CANCELLED")

            try:
                from proofing.utils import call_with_timeout

                success, response = call_with_timeout(
                    tl_model.generate_content,
                    args=(full_prompt,),
                    timeout=180,
                    cancel_flag=cancel_flag
                )

                if not success:
                    if "cancelled" in str(response).lower():
                        log_message(f"[{instructions_label}] Generation cancelled: {response}")
                        raise RuntimeError("GENERATION_CANCELLED")
                    else:
                        log_message(f"[{instructions_label}] Timeout or error during generation: {response}")
                        raise RuntimeError("GENERATION_FAILED")

                log_message(f"[{instructions_label}] Generation succeeded on attempt {attempt + 1}.")
                return response.text

            except concurrent.futures.TimeoutError:
                log_message(f"[{instructions_label}] Timeout after 180s on attempt {attempt + 1}")

            except Exception as e:
                error_str = str(e)
                log_message(f"[{instructions_label}] Error (attempt {attempt + 1}): {error_str}")

                if "Response has no candidates" in error_str:
                    raise RuntimeError("PROHIBITED_CONTENT_BLOCK")

                try:
                    data = json.loads(error_str)
                    block_reason = data.get("prompt_feedback", {}).get("block_reason")
                    if block_reason == "PROHIBITED_CONTENT":
                        raise RuntimeError("PROHIBITED_CONTENT_BLOCK")
                except json.JSONDecodeError:
                    pass

            attempt += 1
            if attempt < max_retries:
                log_message(f"[{instructions_label}] Retrying in {retry_delay}s...")
                time.sleep(retry_delay)
            else:
                raise

    def translate(self, text, log_message=None, cancel_flag=None):
        """
        Translate the given text using the Gemini API.

        Args:
            text: The text to translate
            log_message: Function to log messages
            cancel_flag: Function that returns True if translation should be cancelled
            log_message: Optional callback function for logging
            
        Returns:
            The translated text, or None if translation fails
        """
        if log_message is None:
            log_message = print

        # Extract and store image tags before translation
        image_tag_pattern = re.compile(r'(<img[^>]*>)')
        image_tags = []
        def store_image_tag(match):
            image_tags.append(match.group(1))
            return f"__IMAGE_TAG_{len(image_tags)-1}__"
        
        # Replace image tags with placeholders
        text_with_placeholders = image_tag_pattern.sub(store_image_tag, text)

        # Load the glossary text (could be blank if no file is found or it's empty)
        glossary_path = self.glossary.get_current_glossary_file()
        glossary_text = ""

        if glossary_path:
            glossary_name = os.path.splitext(os.path.basename(glossary_path))[0]
            glossary_dir = os.path.dirname(glossary_path)
            name_glossary_path = os.path.join(glossary_dir, glossary_name, "name_glossary.txt")

            glossary_text = get_matched_name_glossary_entries(name_glossary_path, text, log=log_message)
            if glossary_text:
                log_message("[GLOSSARY] Using matched terms from name_glossary.")
            else:
                log_message("[GLOSSARY] No matching name glossary terms found.")
        else:
            log_message("[GLOSSARY] No glossary file path available.")


        
        # Build instruction sets with glossary
        primary_prompt = get_translation_prompt(self.source_lang)
        secondary_prompt = get_fallback_prompt(self.source_lang)

        # Combine with glossary
        primary_instructions = [primary_prompt]
        secondary_instructions = [secondary_prompt]
        
        if glossary_text:
            primary_instructions.append(glossary_text)
            secondary_instructions.append(glossary_text)


        # Attempt primary instructions first
        try:
            translated = self.generate_with_instructions(
                prompt=text_with_placeholders,
                instructions=primary_instructions,
                instructions_label="PRIMARY",
                log_message=log_message,
                max_retries=3,
                retry_delay=60,
                cancel_flag=cancel_flag
            )

        except RuntimeError as e:
            # Handle cancellation
            if "GENERATION_CANCELLED" in str(e):
                log_message("[PRIMARY] Translation cancelled by user.")
                return None
            # If blocked by content, switch to secondary instructions
            elif "PROHIBITED_CONTENT_BLOCK" in str(e):
                log_message("[PRIMARY] Blocked. Attempting SECONDARY in 5s.")
                time.sleep(5)
                try:
                    translated = self.generate_with_instructions(
                        prompt=text_with_placeholders,
                        instructions=secondary_instructions,
                        instructions_label="SECONDARY",
                        log_message=log_message,
                        max_retries=2,
                        retry_delay=5,
                        cancel_flag=cancel_flag
                    )
                except RuntimeError as e2:
                    if "GENERATION_CANCELLED" in str(e2):
                        log_message("[SECONDARY] Translation cancelled by user.")
                        return None
                    elif "PROHIBITED_CONTENT_BLOCK" in str(e2):
                        log_message("[SECONDARY] Also blocked. Skipping file in 5s.")
                        time.sleep(5)
                        return None
                    else:
                        log_message(f"[SECONDARY] Error: {e2}. Skipping.")
                        return None
            else:
                log_message(f"[PRIMARY] Error: {e}. Skipping.")
                return None

        except Exception as e:
            # Any other fatal error
            log_message(f"[PRIMARY] Fatal: {e}. Skipping.")
            return None

        if not translated:
            return None

        # Restore image tags in the translated text
        def restore_image_tag(match):
            index = int(match.group(1))
            if 0 <= index < len(image_tags):
                return image_tags[index]
            return match.group(0)

        translated = re.sub(r'__IMAGE_TAG_(\d+)__', restore_image_tag, translated)
        return translated

def get_matched_name_glossary_entries(glossary_path, chapter_text, log=None):
    """
    Extract matched glossary entries from the name_glossary.txt based on content in chapter_text.
    """
    matched_entries = []
    seen = set()

    if not os.path.exists(glossary_path):
        return ""

    try:
        with open(glossary_path, "r", encoding="utf-8") as f:
            content = f.read()
        glossary_section = content.split("==================================== GLOSSARY START ===============================")
        if len(glossary_section) < 2:
            return ""
        glossary_text = glossary_section[1].split("==================================== GLOSSARY END ================================")[0].strip()
    except Exception as e:
        if log:
            log(f"[GLOSSARY] Failed to read or parse name glossary: {e}")
        return ""

    for line in glossary_text.splitlines():
        if "=>" not in line:
            continue
        parts = [p.strip() for p in line.split("=>")]
        if len(parts) < 2:
            continue
        original_term = parts[0]
        # Match using regex for word boundaries and ignore whitespace issues
        if re.search(rf'\b{re.escape(original_term)}\b', chapter_text):
            if original_term not in seen:
                matched_entries.append(line.strip())
                seen.add(original_term)

    if log:
        log(f"[GLOSSARY] Matched {len(matched_entries)} terms for this chapter.")
    return "\n".join(matched_entries)

def load_relevant_glossary(glossary_path, chapter_text, log=None):
    matched_entries = []
    seen_keys = set()
    if not os.path.exists(glossary_path):
        return matched_entries

    with open(glossary_path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    for line in lines:
        if "=>" not in line:
            continue
        parts = [p.strip() for p in line.strip().split("=>")]
        if len(parts) != 3:
            continue
        original, translated, pronoun = parts
        if original in chapter_text and original not in seen_keys:
            matched_entries.append(f"{original} => {translated} => {pronoun}")
            seen_keys.add(original)

    if log:
        log(f"[GLOSSARY] Matched {len(matched_entries)} glossary terms to this chapter.")

    return matched_entries


