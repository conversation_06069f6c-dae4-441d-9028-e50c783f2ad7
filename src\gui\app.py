
import os
import shutil
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
from datetime import datetime

# Ensure src/ is in sys.path so imports work correctly if run from GUI
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from chapter_splitting_tools.epub_separator import EPUBSeparator
from chapter_splitting_tools.novel_splitter import TextSplitterApp
from chapter_splitting_tools.output_combiner import Output<PERSON>ombiner
from chapter_splitting_tools.folder_manager import FolderManager
from translation.translationManager import main as translation_main

class TranslationApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Novel Translation Tool")
        self.root.geometry("1200x600")

        # Threading flags
        self.pause_event = threading.Event()
        self.pause_event.set()
        self.cancel_requested = False

        self.input_folder = None
        self.glossary_file = None

        # GUI state
        self.skip_to_proofing = tk.BooleanVar(value=False)
        self.skip_to_translation = tk.BooleanVar(value=False)
        self.language_var = tk.StringVar(value="Japanese")

        self._build_ui()
        self._create_default_folders()
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

    def on_close(self):
        self.cancel_requested = True
        self.pause_event.set()  # Resume anything paused
        self.log_message("[CONTROL] Shutting down.")
        self.root.destroy()


    def _build_ui(self):
        self.main_frame = tk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.text_frame = tk.Frame(self.main_frame)
        self.text_frame.pack(fill=tk.BOTH, expand=True)

        self.text_area = tk.Text(self.text_frame, wrap=tk.WORD, height=20)
        self.text_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = tk.Scrollbar(self.text_frame, command=self.text_area.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.text_area.config(yscrollcommand=scrollbar.set)

        self.button_frame = tk.Frame(self.main_frame)
        self.button_frame.pack(fill=tk.X, pady=10)

        # Buttons
        tk.Button(self.button_frame, text="Run Translation", command=self.run_translation).pack(side=tk.LEFT, padx=5)
        tk.Button(self.button_frame, text="Split Chapters", command=self.show_splitter_dialog).pack(side=tk.LEFT, padx=5)
        tk.Button(self.button_frame, text="Combine Output", command=self.run_output_combiner).pack(side=tk.LEFT, padx=5)
        tk.Button(self.button_frame, text="Clear Folders", command=self.clear_folders).pack(side=tk.LEFT, padx=5)
        tk.Button(self.button_frame, text="Config", command=self.show_config_dialog).pack(side=tk.LEFT, padx=5)
        self.pause_button = tk.Button(self.button_frame, text="Pause", command=self.toggle_pause)
        self.pause_button.pack(side=tk.LEFT, padx=5)
        tk.Button(self.button_frame, text="Stop", command=self.stop_translation).pack(side=tk.LEFT, padx=5)
        tk.Button(self.button_frame, text="Organize Translated Folders", command=self.organize_translated_folders).pack(side=tk.LEFT, padx=5)

        # Replace checkboxes with a dropdown for phase selection
        ttk.Label(self.button_frame, text="Start From Phase:").pack(side=tk.LEFT, padx=5)
        self.phase_var = tk.StringVar(value="Phase 1: Glossary")
        ttk.Combobox(
            self.button_frame,
            textvariable=self.phase_var,
            values=[
                "Phase 1: Glossary", 
                "Phase 2: Translation", 
                "Phase 3.1: Non-English Check",
                "Phase 3.2: Gender Proofing",
                "Phase 3.3: Final Proofing"
            ],
            state="readonly",
            width=20
        ).pack(side=tk.LEFT, padx=5)

        # Language selector
        ttk.Label(self.button_frame, text="Source Language:").pack(side=tk.LEFT, padx=5)
        ttk.Combobox(
            self.button_frame,
            textvariable=self.language_var,
            values=["Japanese", "Chinese", "Korean"],
            state="readonly",
            width=12
        ).pack(side=tk.LEFT, padx=5)

        # === Bulk Translation Job Panel ===
        self.bulk_frame = tk.Frame(self.main_frame)
        self.bulk_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        ttk.Label(self.bulk_frame, text="Bulk Translation Jobs").pack(anchor="w")
        self.job_tree = ttk.Treeview(self.bulk_frame, columns=("status",), show="headings", height=8)
        self.job_tree.heading("status", text="Status")
        self.job_tree.column("status", width=100, anchor="center")
        self.job_tree.pack(fill=tk.BOTH, expand=True)

        tk.Button(self.bulk_frame, text="Run Bulk Translation", command=self.run_bulk_translation).pack(pady=5)


    def _create_default_folders(self):
        for folder in ["input", "output", "translation", os.path.join("translation", "glossary")]:
            os.makedirs(folder, exist_ok=True)
            self.log_message(f"[INIT] Ensured folder exists: {folder}")

    def log_message(self, *args):
        timestamp = datetime.now().strftime("[%H:%M:%S]")
        message = f"{timestamp} " + " ".join(str(arg) for arg in args)

        try:
            self.text_area.insert(tk.END, message + "\n")
            self.text_area.see(tk.END)
            self.text_area.update_idletasks()
        except Exception:
            print("[LOG]", message)

    def run_translation(self):
        self.translate_button = self.button_frame.children.get("!button")
        if self.translate_button:
            self.translate_button.config(state=tk.DISABLED)

        input_folder = self.select_input_folder()
        if not input_folder:
            self.log_message("No input folder selected. Aborting.")
            return
        self._move_files_to_input(input_folder)

        glossary_file = self.select_glossary_file()
        self.glossary_file = glossary_file
        if not glossary_file:
            self.glossary_file = None  # Ensure it's recognized as "no glossary selected"


        selected_lang = self.language_var.get()

        def worker():
            try:
                selected_phase = self.phase_var.get()
                
                # Map dropdown selection to parameters
                skip_to_proofing = selected_phase.startswith("Phase 3")
                skip_to_translation = selected_phase.startswith("Phase 2") or skip_to_proofing
                
                # For sub-phases of proofing
                proofing_subphase = None
                if "3.1" in selected_phase:
                    proofing_subphase = "non_english"
                elif "3.2" in selected_phase:
                    proofing_subphase = "gender"
                elif "3.3" in selected_phase:
                    proofing_subphase = "final"
                
                translation_main(
                    log_message=self.log_message,
                    glossary_file=self.glossary_file or None,
                    proofing_only=skip_to_proofing,
                    skip_phase1=skip_to_translation,
                    proofing_subphase=proofing_subphase,
                    pause_event=self.pause_event,
                    cancel_flag=lambda: self.cancel_requested,
                    source_lang=selected_lang,
                    input_folder=self.input_folder
                )

                # Update reference to glossary file in case one was auto-created
                if not self.glossary_file and os.path.exists("input"):
                    input_name = os.path.basename(self.input_folder.rstrip("/\\"))
                    auto_gloss_path = os.path.join("translation", "glossary", f"{input_name}.txt")
                    if os.path.exists(auto_gloss_path):
                        self.glossary_file = auto_gloss_path
                        self.log_message(f"[INFO] Glossary file set to auto-created: {self.glossary_file}")

            except Exception as e:
                self.log_message("[ERROR]", e)
            finally:
                self.pause_event.set()
                self.cancel_requested = False
                self.pause_button.config(text="Pause")
                self.translate_button.config(state=tk.NORMAL)

        threading.Thread(target=worker, daemon=True).start()

    def select_input_folder(self):
        folder = filedialog.askdirectory(title="Select Input Folder", initialdir="input")
        if folder:
            self.input_folder = folder
            self.log_message(f"Selected input folder: {os.path.basename(folder)}")
        return folder

    def select_glossary_file(self):
        default_dir = os.path.join(os.path.dirname(__file__), "..", "translation")
        file_path = filedialog.askopenfilename(title="Select Glossary", initialdir=default_dir, filetypes=[("Text files", "*.txt")])
        return file_path

    def _move_files_to_input(self, src):
        for filename in os.listdir(src):
            shutil.move(os.path.join(src, filename), os.path.join("input", filename))
            self.log_message(f"Moved {filename} to input folder")

    def show_splitter_dialog(self):
        dialog = tk.Toplevel(self.root)
        dialog.title("Choose Splitter")
        dialog.geometry("300x180")

        tk.Label(dialog, text="Choose splitter type:").pack(pady=10)
        tk.Button(dialog, text="Novel Splitter", command=lambda: [dialog.destroy(), self.run_novel_splitter()]).pack(pady=5)
        tk.Button(dialog, text="EPUB Separator", command=lambda: [dialog.destroy(), self.run_epub_separator()]).pack(pady=5)
        tk.Button(dialog, text="Bulk EPUB Separator", command=lambda: [dialog.destroy(), self.run_bulk_epub_separator()]).pack(pady=5)

    def run_novel_splitter(self):
        self.log_message("Starting Novel Splitter")
        TextSplitterApp(tk.Toplevel(self.root))

    def run_epub_separator(self):
        epub_file = filedialog.askopenfilename(title="Select EPUB", filetypes=[("EPUB files", "*.epub")])
        if not epub_file:
            return
        epub_name = os.path.splitext(os.path.basename(epub_file))[0]
        input_subdir = os.path.join("input", epub_name)

        self.log_message(f"Running EPUB Separator for {epub_name}")
        separator = EPUBSeparator(self.log_message)
        separator.separate(epub_file, input_subdir)

    def run_output_combiner(self):
        folder = filedialog.askdirectory(title="Select Output Folder", initialdir="output")
        if folder:
            combiner = OutputCombiner(self.log_message)
            combiner.show_save_dialog(folder)

    def clear_folders(self):
        manager = FolderManager(self.log_message)
        manager.show_clear_dialog()

    def toggle_pause(self):
        if self.pause_event.is_set():
            self.pause_event.clear()
            self.pause_button.config(text="Resume")
            self.log_message("[CONTROL] Paused")
        else:
            self.pause_event.set()
            self.pause_button.config(text="Pause")
            self.log_message("[CONTROL] Resumed")

    def stop_translation(self):
        self.cancel_requested = True
        self.pause_event.set()
        self.log_message("[CONTROL] Stop requested. Will stop after current chapter.")
        
        # Reset UI after a short delay to ensure the worker thread has time to respond
        self.root.after(1000, self.reset_ui_after_cancel)
    
    def reset_ui_after_cancel(self):
        # Reset UI state regardless of worker thread status
        self.cancel_requested = False
        self.pause_button.config(text="Pause")
        self.translate_button.config(state=tk.NORMAL)
        self.log_message("[CONTROL] Translation stopped. UI reset.")

    def organize_translated_folders(self):
        # Prompt if no input folder or if "input" base folder is selected
        if not self.input_folder or os.path.basename(self.input_folder.rstrip("/\\")) == "input":
            selected_folder = filedialog.askdirectory(title="Select Specific Input Subfolder", initialdir="input")
            if not selected_folder:
                self.log_message("[CANCELLED] No valid input folder selected.")
                return
            self.input_folder = selected_folder
            self.log_message(f"[SELECTED] Input folder set to: {os.path.basename(selected_folder)}")

        input_name = os.path.basename(self.input_folder.rstrip("/\\"))
        try:
            from chapter_splitting_tools.organize_translated_folders import move_translated_content
            move_translated_content(input_name, log=self.log_message)
        except Exception as e:
            self.log_message(f"[ERROR] Failed to run folder organizer: {e}")

    def run_bulk_epub_separator(self):
        separator = EPUBSeparator(self.log_message)
        separator.bulk_split_with_dialog(base_output_dir="input")


    def run_bulk_translation(self):
        selected_dirs = filedialog.askdirectory(mustexist=True, title="Select Parent Folder for Input Subfolders", initialdir="input")
        if not selected_dirs:
            self.log_message("[CANCELLED] No folder selected.")
            return

        # List subfolders in selected directory
        subdirs = [d for d in os.listdir(selected_dirs) if os.path.isdir(os.path.join(selected_dirs, d))]
        if not subdirs:
            self.log_message("[INFO] No subfolders found in selected directory.")
            return

        # Clear job tree
        for i in self.job_tree.get_children():
            self.job_tree.delete(i)

        # Enqueue jobs
        jobs = []
        for sub in subdirs:
            path = os.path.join(selected_dirs, sub)
            jobs.append((sub, path))
            self.job_tree.insert("", "end", iid=sub, values=("Pending",))

        def worker():
            for name, folder_path in jobs:
                # Check if cancel was requested before starting a new job
                if self.cancel_requested:
                    self.log_message("[CONTROL] Bulk translation cancelled.")
                    break
                
                self.job_tree.item(name, values=("Translating",))
                self.input_folder = folder_path  # needed for organizing later
                
                # Create input folder if it doesn't exist
                input_dir = os.path.join("input", name)
                os.makedirs(input_dir, exist_ok=True)
                
                # Check for pause before file operations
                if not self.pause_event.is_set():
                    self.log_message("[CONTROL] Paused before processing folder: " + name)
                    self.pause_event.wait()
                    if self.cancel_requested:
                        self.log_message("[CONTROL] Cancelled after pause.")
                        break
                
                # Move contents to input folder
                self.log_message(f"[SETUP] Moving contents from {folder_path} to {input_dir}")
                for item in os.listdir(folder_path):
                    src = os.path.join(folder_path, item)
                    dst = os.path.join(input_dir, item)
                    if os.path.exists(dst):
                        if os.path.isdir(dst):
                            shutil.rmtree(dst)
                        else:
                            os.remove(dst)
                    if os.path.isdir(src):
                        shutil.copytree(src, dst)
                    else:
                        shutil.copy2(src, dst)

                try:
                    # Check for pause/cancel again before starting translation
                    if self.cancel_requested:
                        self.log_message("[CONTROL] Cancelled before translation.")
                        break
                        
                    if not self.pause_event.is_set():
                        self.log_message("[CONTROL] Paused before translation.")
                        self.pause_event.wait()
                        if self.cancel_requested:
                            self.log_message("[CONTROL] Cancelled after pause.")
                            break
                    
                    # Run translation with pause_event and cancel_flag
                    translation_main(
                        log_message=self.log_message,
                        glossary_file=None,
                        proofing_only=False,
                        skip_phase1=False,
                        proofing_subphase=None,
                        pause_event=self.pause_event,
                        cancel_flag=lambda: self.cancel_requested,
                        source_lang=self.language_var.get(),
                        input_folder=input_dir
                    )
                    
                    # Check if cancelled after translation
                    if self.cancel_requested:
                        self.log_message("[CONTROL] Cancelled after translation.")
                        break
                        
                    self.job_tree.item(name, values=("Organizing",))
                    
                    # Check for pause before organizing
                    if not self.pause_event.is_set():
                        self.log_message("[CONTROL] Paused before organizing.")
                        self.pause_event.wait()
                        if self.cancel_requested:
                            self.log_message("[CONTROL] Cancelled after pause.")
                            break
                    
                    from chapter_splitting_tools.organize_translated_folders import move_translated_content
                    move_translated_content(name, log=self.log_message)
                    self.job_tree.item(name, values=("Done",))
                except Exception as e:
                    self.log_message(f"[ERROR] Failed to translate {name}: {e}")
                    self.job_tree.item(name, values=("Error",))
                    
            # After all jobs or after cancellation, reset UI
            if self.cancel_requested:
                self.reset_ui_after_cancel()

        # Start worker thread
        self.translate_button.config(state=tk.DISABLED)
        threading.Thread(target=worker, daemon=True).start()

    def show_config_dialog(self):
        """Show configuration dialog for updating AI service settings."""
        dialog = tk.Toplevel(self.root)
        dialog.title("AI Service Configuration")
        dialog.geometry("500x400")
        dialog.resizable(False, False)

        # Make dialog modal
        dialog.transient(self.root)
        dialog.grab_set()

        # Main frame
        main_frame = tk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        tk.Label(main_frame, text="AI Service Configuration", font=("Arial", 14, "bold")).pack(pady=(0, 20))

        # Current config display
        config_frame = tk.LabelFrame(main_frame, text="Current Configuration", padx=10, pady=10)
        config_frame.pack(fill=tk.X, pady=(0, 20))

        # Load current config
        current_config = self._load_current_config()

        tk.Label(config_frame, text=f"Project ID: {current_config.get('PROJECT_ID', 'Not set')}", anchor="w").pack(fill=tk.X)
        tk.Label(config_frame, text=f"Location: {current_config.get('LOCATION', 'Not set')}", anchor="w").pack(fill=tk.X)
        tk.Label(config_frame, text=f"Service Account: {'✓ Configured' if current_config.get('service_account_exists') else '✗ Missing'}", anchor="w").pack(fill=tk.X)

        # Configuration inputs
        input_frame = tk.LabelFrame(main_frame, text="Update Configuration", padx=10, pady=10)
        input_frame.pack(fill=tk.X, pady=(0, 20))

        # Project ID
        tk.Label(input_frame, text="Project ID:").pack(anchor="w")
        project_id_var = tk.StringVar(value=current_config.get('PROJECT_ID', ''))
        tk.Entry(input_frame, textvariable=project_id_var, width=50).pack(fill=tk.X, pady=(0, 10))

        # Location
        tk.Label(input_frame, text="Location:").pack(anchor="w")
        location_var = tk.StringVar(value=current_config.get('LOCATION', 'us-central1'))
        location_combo = ttk.Combobox(input_frame, textvariable=location_var, values=[
            "us-central1", "us-east1", "us-west1", "europe-west1", "asia-southeast1"
        ], state="readonly")
        location_combo.pack(fill=tk.X, pady=(0, 10))

        # Service Account JSON upload
        tk.Label(input_frame, text="Service Account JSON:").pack(anchor="w")
        upload_frame = tk.Frame(input_frame)
        upload_frame.pack(fill=tk.X, pady=(0, 10))

        self.selected_json_file = tk.StringVar(value="No file selected")
        tk.Label(upload_frame, textvariable=self.selected_json_file, relief="sunken", anchor="w").pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        tk.Button(upload_frame, text="Browse", command=lambda: self._browse_service_account_file(dialog)).pack(side=tk.RIGHT)

        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        tk.Button(button_frame, text="Save Configuration",
                 command=lambda: self._save_config(dialog, project_id_var.get(), location_var.get())).pack(side=tk.LEFT, padx=(0, 10))
        tk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT)

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

    def _load_current_config(self):
        """Load current configuration from config files."""
        config = {}

        # Load config.txt
        config_file = os.path.join("src", "config", "config.txt")
        if os.path.exists(config_file):
            with open(config_file, "r", encoding="utf-8") as f:
                for line in f:
                    if "=" in line:
                        key, value = line.strip().split("=", 1)
                        config[key.strip()] = value.strip()

        # Check if service account exists
        service_account_file = os.path.join("src", "config", "service_account.json")
        config['service_account_exists'] = os.path.exists(service_account_file)

        return config

    def _browse_service_account_file(self, parent_dialog):
        """Browse for service account JSON file."""
        file_path = filedialog.askopenfilename(
            parent=parent_dialog,
            title="Select Service Account JSON",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            self.selected_json_file.set(os.path.basename(file_path))
            self.temp_service_account_path = file_path

    def _save_config(self, dialog, project_id, location):
        """Save the updated configuration."""
        try:
            # Update config.txt
            config_file = os.path.join("src", "config", "config.txt")
            os.makedirs(os.path.dirname(config_file), exist_ok=True)

            with open(config_file, "w", encoding="utf-8") as f:
                f.write(f"PROJECT_ID={project_id}\n")
                f.write(f"LOCATION={location}\n")
                if hasattr(self, 'temp_service_account_path'):
                    # Extract LOGIN_KEY from the new service account file
                    try:
                        import json
                        with open(self.temp_service_account_path, 'r') as sa_file:
                            sa_data = json.load(sa_file)
                            if 'private_key_id' in sa_data:
                                f.write(f"LOGIN_KEY={sa_data['private_key_id']}\n")
                    except Exception as e:
                        self.log_message(f"[WARNING] Could not extract LOGIN_KEY: {e}")

            # Copy service account file if selected
            if hasattr(self, 'temp_service_account_path'):
                service_account_dest = os.path.join("src", "config", "service_account.json")
                shutil.copy2(self.temp_service_account_path, service_account_dest)
                self.log_message(f"[CONFIG] Service account file updated: {os.path.basename(self.temp_service_account_path)}")
                delattr(self, 'temp_service_account_path')

            self.log_message(f"[CONFIG] Configuration updated - Project: {project_id}, Location: {location}")

            # Show restart notice
            messagebox.showinfo(
                "Configuration Updated",
                "Configuration has been saved successfully.\n\n"
                "Please restart the application for changes to take effect.",
                parent=dialog
            )

            dialog.destroy()

        except Exception as e:
            self.log_message(f"[ERROR] Failed to save configuration: {e}")
            messagebox.showerror("Error", f"Failed to save configuration:\n{e}", parent=dialog)


